'use client';

import { useCart } from '@/context/CartContext';
import { useState, useEffect } from 'react';
import { FiPlus, FiMinus, FiX, FiChevronRight, FiTag } from 'react-icons/fi';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'react-hot-toast';
import Image from 'next/image';

export default function CartPage() {
  const {
    cart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    validateCartStock,
    coupon,
    applyCoupon,
    removeCoupon,
    calculateCouponDiscount
  } = useCart();
  const [mounted, setMounted] = useState(false);
  const [isValidatingStock, setIsValidatingStock] = useState(false);
  const [couponCode, setCouponCode] = useState('');
  const [isApplyingCoupon, setIsApplyingCoupon] = useState(false);
  const router = useRouter();
  const { user } = useAuth();

  useEffect(() => {
    setMounted(true);

    // Validate cart stock when component mounts
    const validateStock = async () => {
      if (cart && cart.length > 0) {
        setIsValidatingStock(true);
        await validateCartStock();
        setIsValidatingStock(false);
      }
    };

    validateStock();
  }, [cart, validateCartStock]);

  const getDiscountedPrice = (item) => {
    if (!item.discount || item.discount <= 0) return Number(item.price) || 0;
    return (Number(item.price) * (100 - Number(item.discount))) / 100;
  };

  const calculateSubtotal = () => {
    return cart.reduce((total, item) => {
      const price = getDiscountedPrice(item);
      const quantity = Number(item.quantity) || 0;
      return total + (price * quantity);
    }, 0);
  };

  const calculateShipping = () => {
    return 0; // Free shipping
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const shipping = calculateShipping();
    const couponDiscount = coupon ? calculateCouponDiscount(subtotal) : 0;

    return subtotal + shipping - couponDiscount;
  };

  const handleApplyCoupon = async () => {
    if (!couponCode.trim()) {
      toast.error('Please enter a coupon code');
      return;
    }

    setIsApplyingCoupon(true);
    try {
      await applyCoupon(couponCode.trim());
      setCouponCode(''); // Clear input after successful application
    } catch (error) {
      console.error('Error applying coupon:', error);
    } finally {
      setIsApplyingCoupon(false);
    }
  };

  const formatPrice = (price) => {
    const numPrice = Number(price);
    return !isNaN(numPrice) ? numPrice.toLocaleString('en-IN') : '0';
  };

  const handleQuantityUpdate = async (itemId, newQuantity) => {
    if (newQuantity < 1) return;
    try {
      const success = await updateCartItemQuantity(itemId, newQuantity);
      if (!success) {
        toast.error('Failed to update quantity');
      }
    } catch (error) {
      toast.error('Failed to update quantity');
    }
  };

  const handleRemoveItem = async (itemId) => {
    try {
      const success = await removeFromCart(itemId);
      if (success) {
        toast.success('Item removed');
      } else {
        toast.error('Failed to remove item');
      }
    } catch (error) {
      toast.error('Failed to remove item');
    }
  };

  const handleClearCart = async () => {
    try {
      const success = await clearCart();
      if (success) {
        toast.success('Cart cleared');
      } else {
        toast.error('Failed to clear cart');
      }
    } catch (error) {
      toast.error('Failed to clear cart');
    }
  };

  const handleCheckout = async () => {
    if (!user) {
      toast.error('Please login to checkout');
      router.push('/login');
      return;
    }
    if (cart.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    // Validate stock before proceeding to checkout
    setIsValidatingStock(true);
    const isStockValid = await validateCartStock();
    setIsValidatingStock(false);

    if (!isStockValid) {
      toast.error('Some items in your cart are out of stock. Please review your cart before proceeding.');
      return;
    }

    router.push('/checkout');
  };

  if (!mounted) {
    return null;
  }

  if (!cart || cart.length === 0) {
    return (
      <div className="min-h-screen bg-white pt-20">
        <div className="max-w-6xl mx-auto px-4 xl:px-0 py-16">
          <div className="text-center">
            <h2 className="text-xl font-light mb-6">YOUR SHOPPING CART IS EMPTY</h2>
            <p className="text-sm text-gray-600 mb-10 max-w-lg mx-auto">
              You haven't added any items to your CART yet.
            </p>
            <Link
              href="/products"
              className="inline-block px-8 py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200"
            >
              Shop New Collection
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-20">
      <div className="max-w-6xl mx-auto px-4 xl:px-0 py-12">
        <h1 className="text-2xl font-light mb-2 text-center">SHOPPING CART</h1>
        <p className="text-sm text-center text-gray-500 mb-10">
          {cart.length} {cart.length === 1 ? 'item' : 'items'}
        </p>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-10">
          {/* Left side - Cart Items */}
          <div className="lg:col-span-2">
            <div className="space-y-8">
              {cart.map((item) => (
                <div key={item._id} className="border-b border-gray-100 pb-8">
                  <div className="flex flex-col md:flex-row gap-6">
                    {/* Product Image - 4:5 aspect ratio on all screens */}
                    <div className="w-full aspect-[4/5] md:w-40 md:aspect-[4/5] relative flex-shrink-0">
                      <Image
                        src={item.images?.[0] || '/placeholder.jpg'}
                        alt={item.name || 'Product'}
                        fill
                        className="object-cover object-center"
                      />
                    </div>

                    {/* Product Details */}
                    <div className="flex-grow flex flex-col">
                      <div className="flex justify-between">
                        <div>
                          <Link href={`/products/${item._id}`}>
                            <h3 className="text-sm text-black hover:opacity-70 transition-opacity duration-200">
                              {item.name || 'Unnamed Product'}
                            </h3>
                          </Link>
                          <p className="text-xs text-gray-500 mt-1">
                            {item.category || 'Uncategorized'}
                          </p>
                        </div>
                        <button
                          onClick={() => handleRemoveItem(item._id)}
                          className="p-1 text-black hover:opacity-70 transition-opacity duration-200"
                          aria-label="Remove item"
                        >
                          <FiX size={16} />
                        </button>
                      </div>

                      <div className="mt-auto flex justify-between items-end">
                        {/* Quantity Controls */}
                        <div className="flex items-center gap-4">
                          <button
                            onClick={() => handleQuantityUpdate(item._id, (Number(item.quantity) || 1) - 1)}
                            disabled={(Number(item.quantity) || 0) <= 1}
                            className="text-black disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <FiMinus size={16} />
                          </button>
                          <span className="text-sm">{Number(item.quantity) || 1}</span>
                          <button
                            onClick={() => handleQuantityUpdate(item._id, (Number(item.quantity) || 1) + 1)}
                            className="text-black"
                          >
                            <FiPlus size={16} />
                          </button>
                        </div>

                        {/* Price */}
                        <div className="text-right">
                          {item.discount && item.discount > 0 ? (
                            <>
                              <div className="text-sm font-medium">
                                ₹{formatPrice(getDiscountedPrice(item) * (Number(item.quantity) || 1))}
                              </div>
                              <div className="text-xs text-gray-500 line-through">
                                ₹{formatPrice(item.price * (Number(item.quantity) || 1))}
                              </div>
                              <div className="text-xs text-green-600 mt-1">
                                {item.discount}% OFF
                              </div>
                            </>
                          ) : (
                            <div className="text-sm font-medium">
                              ₹{formatPrice(item.price * (Number(item.quantity) || 1))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right side - Order Summary */}
          <div className="lg:col-span-1">
            <div className="border-t border-b border-gray-100 py-8">
              <h2 className="text-sm uppercase tracking-wider mb-6">Order Summary</h2>

              <div className="space-y-4">
                {/* Original Price Total */}
                <div className="flex justify-between text-sm">
                  <span>Original Price</span>
                  <span>₹{formatPrice(cart.reduce((total, item) => {
                    const price = Number(item.price) || 0;
                    const quantity = Number(item.quantity) || 0;
                    return total + (price * quantity);
                  }, 0))}</span>
                </div>

                {/* Discount */}
                {cart.some(item => item.discount > 0) && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Discount</span>
                    <span>-₹{formatPrice(cart.reduce((total, item) => {
                      if (!item.discount || item.discount <= 0) return total;
                      const originalPrice = Number(item.price) || 0;
                      const discountedPrice = getDiscountedPrice(item);
                      const quantity = Number(item.quantity) || 0;
                      return total + ((originalPrice - discountedPrice) * quantity);
                    }, 0))}</span>
                  </div>
                )}

                {/* Subtotal after discount */}
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>₹{formatPrice(calculateSubtotal())}</span>
                </div>

                {/* Shipping */}
                <div className="flex justify-between text-sm">
                  <span>Shipping</span>
                  <span>Free</span>
                </div>

                {/* Coupon Discount */}
                {coupon && (
                  <div className="flex justify-between text-sm text-green-600">
                    <span>Coupon Discount ({coupon.code})</span>
                    <span>-₹{formatPrice(calculateCouponDiscount(calculateSubtotal()))}</span>
                  </div>
                )}

                {/* Total */}
                <div className="border-t border-gray-100 pt-4 mt-6">
                  <div className="flex justify-between text-sm font-medium">
                    <span>Total</span>
                    <span>₹{formatPrice(calculateTotal())}</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">Including GST</p>
                </div>
              </div>

              {/* Coupon Code Input */}
              <div className="mt-6 border-t border-gray-100 pt-6">
                <h3 className="text-sm mb-3">Apply Coupon Code</h3>
                <div className="flex gap-2">
                  <div className="relative flex-grow">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FiTag className="text-gray-400" size={16} />
                    </div>
                    <input
                      type="text"
                      value={couponCode}
                      onChange={(e) => setCouponCode(e.target.value.toUpperCase())}
                      placeholder="ENTER COUPON CODE"
                      className="w-full pl-10 pr-3 py-2 border border-gray-200 text-sm focus:ring-0 focus:border-black uppercase"
                    />
                  </div>
                  {coupon ? (
                    <button
                      onClick={removeCoupon}
                      className="px-4 py-2 bg-red-50 text-red-600 text-xs uppercase tracking-wider hover:bg-red-100 transition-colors duration-200"
                    >
                      Remove
                    </button>
                  ) : (
                    <button
                      onClick={handleApplyCoupon}
                      disabled={isApplyingCoupon || !couponCode.trim()}
                      className="px-4 py-2 bg-black text-white text-xs uppercase tracking-wider hover:bg-gray-900 transition-colors duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed"
                    >
                      {isApplyingCoupon ? 'Applying...' : 'Apply'}
                    </button>
                  )}
                </div>
                {coupon && (
                  <p className="text-xs text-green-600 mt-2">
                    {coupon.type === 'percentage'
                      ? `${coupon.value}% off`
                      : `₹${formatPrice(coupon.value)} off`}
                    {coupon.minPurchase > 0 && ` on orders above ₹${formatPrice(coupon.minPurchase)}`}
                  </p>
                )}
              </div>

              <div className="mt-8 space-y-4">
                <button
                  onClick={handleCheckout}
                  disabled={isValidatingStock}
                  className="w-full py-3 bg-black text-white uppercase text-xs tracking-wider hover:bg-gray-900 transition-colors duration-200 disabled:bg-gray-400 disabled:cursor-not-allowed"
                >
                  {isValidatingStock ? 'Validating Stock...' : 'Proceed to Checkout'}
                </button>

                <button
                  onClick={handleClearCart}
                  className="w-full py-3 border border-black text-black uppercase text-xs tracking-wider hover:bg-black hover:text-white transition-colors duration-200"
                >
                  Clear CART
                </button>
              </div>

              <div className="mt-8">
                <Link
                  href="/products"
                  className="flex items-center text-xs uppercase hover:opacity-70 transition-opacity duration-200"
                >
                  <span>Continue Shopping</span>
                  <FiChevronRight size={12} className="ml-1" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}