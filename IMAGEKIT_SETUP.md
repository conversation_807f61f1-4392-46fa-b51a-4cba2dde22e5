# ImageKit Integration Setup Guide

This guide explains how to set up ImageKit for image upload functionality in the admin products creation page.

## Prerequisites

1. An ImageKit account (sign up at https://imagekit.io)
2. ImageKit credentials (Public Key, Private Key, and URL Endpoint)

## Setup Steps

### 1. Get ImageKit Credentials

1. Log in to your ImageKit dashboard
2. Go to Developer Options > API Keys
3. Copy the following credentials:
   - **Public Key**: Used for client-side operations
   - **Private Key**: Used for server-side authentication (keep this secret!)
   - **URL Endpoint**: Your ImageKit URL endpoint (e.g., `https://ik.imagekit.io/your_imagekit_id`)

### 2. Update Environment Variables

Update your `.env.local` file with your actual ImageKit credentials:

```env
# ImageKit Configuration
NEXT_PUBLIC_IMAGEKIT_PUBLIC_KEY=your_actual_public_key_here
IMAGEKIT_PRIVATE_KEY=your_actual_private_key_here
NEXT_PUBLIC_IMAGEKIT_URL_ENDPOINT=https://ik.imagekit.io/your_actual_imagekit_id
```

**Important**: 
- Replace the placeholder values with your actual ImageKit credentials
- Never commit your private key to version control
- The `NEXT_PUBLIC_` prefix makes the variable available on the client side

### 3. Test the Integration

1. Start the development server: `npm run dev`
2. Navigate to `/admin/products/new`
3. Go to the "Images" section
4. Click "CLICK TO UPLOAD IMAGE" or "UPLOAD" button
5. Select an image file
6. The image should upload to ImageKit and appear in the product images grid

## Features

- **Secure Upload**: Uses server-side authentication to generate secure upload tokens
- **Admin Only**: Only authenticated admin users can upload images
- **Organized Storage**: Images are stored in `/products` folder in ImageKit
- **Unique Filenames**: Automatically generates unique filenames to prevent conflicts
- **Progress Tracking**: Shows upload progress and status messages
- **Error Handling**: Comprehensive error handling with user-friendly messages

## File Structure

```
src/
├── components/
│   └── ImageKitUpload.js          # Main upload component
├── app/api/imagekit/auth/
│   └── route.js                   # Authentication endpoint
├── lib/
│   └── imagekit-server.js         # Server-side utilities
└── components/
    └── AdminProductForm.js        # Updated to use ImageKit
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check that your private key is correct and properly set in environment variables
2. **Upload Failed**: Verify your public key and URL endpoint are correct
3. **Access Denied**: Ensure you're logged in as an admin user

### Environment Variables Not Loading

If environment variables aren't loading:
1. Restart the development server
2. Check that `.env.local` is in the project root
3. Verify variable names match exactly (case-sensitive)

## Security Notes

- Private key is only used on the server side
- Upload tokens expire after 30 minutes for security
- Only admin users can access the upload functionality
- Images are stored in a dedicated `/products` folder

## Next Steps

After setting up ImageKit:
1. Test uploading various image formats
2. Verify images appear correctly in the product creation form
3. Test the complete product creation flow
4. Consider setting up ImageKit transformations for optimized delivery
