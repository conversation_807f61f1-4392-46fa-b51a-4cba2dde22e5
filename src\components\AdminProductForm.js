'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'react-hot-toast';
import Image from 'next/image';

export default function AdminProductForm({ product = null }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [activeSection, setActiveSection] = useState('details'); // 'details' or 'images'
  const [imageUrl, setImageUrl] = useState('');
  const [formData, setFormData] = useState({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || '',
    category: product?.category || '',
    stock: product?.stock || '',
    images: product?.images || [],
    discount: product?.discount || 0
  });

  const categories = [
    'LIVING ROOM',
    'BEDROOM',
    'DINING ROOM',
    'OFFICE',
    'OUTDOOR',
    'KITCHEN',
    'BATHROOM',
    'STORAGE',
    'DECOR'
  ];

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleAddImage = async () => {
    if (!imageUrl.trim()) {
      toast.error('Please enter an image URL');
      return;
    }

    setFormData(prev => ({
      ...prev,
      images: [...prev.images, imageUrl]
    }));
    setImageUrl('');
  };

  const removeImage = (index) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (formData.images.length === 0) {
        toast.error('Please add at least one product image');
        return;
      }

      const response = await fetch(
        product ? `/api/admin/products/${product._id}` : '/api/admin/products',
        {
          method: product ? 'PUT' : 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ...formData,
            price: Number(formData.price),
            stock: Number(formData.stock),
            discount: Number(formData.discount || 0),
            isPublished: true
          }),
        }
      );

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to save product');
      }

      toast.success(product ? 'Product updated successfully' : 'Product created successfully');
      router.push('/admin/products');
    } catch (error) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Navigation Tabs */}
      <div className="sticky top-[60px] bg-white z-40 border-b border-black/10">
        <div className="flex justify-center space-x-12 px-6 py-4">
          <button
            onClick={() => setActiveSection('details')}
            className={`text-sm tracking-widest ${
              activeSection === 'details' ? 'opacity-100' : 'opacity-50'
            }`}
          >
            DETAILS
          </button>
          <button
            onClick={() => setActiveSection('images')}
            className={`text-sm tracking-widest ${
              activeSection === 'images' ? 'opacity-100' : 'opacity-50'
            }`}
          >
            IMAGES ({formData.images.length})
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="px-6 py-8">
        <AnimatePresence mode="wait">
          {activeSection === 'details' ? (
            <motion.div
              key="details"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Name */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  PRODUCT NAME
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black"
                />
              </div>

              {/* Category */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  CATEGORY
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleChange}
                  required
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black"
                >
                  <option value="">SELECT CATEGORY</option>
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category}
                    </option>
                  ))}
                </select>
              </div>

              {/* Price */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  PRICE (₹)
                </label>
                <input
                  type="number"
                  name="price"
                  value={formData.price}
                  onChange={handleChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black"
                />
              </div>

              {/* Discount */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  DISCOUNT (%)
                </label>
                <input
                  type="number"
                  name="discount"
                  value={formData.discount}
                  onChange={handleChange}
                  min="0"
                  max="100"
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black"
                />
                {formData.discount > 0 && formData.price > 0 && (
                  <p className="text-xs text-gray-500 mt-1">
                    Final price: ₹{((formData.price * (100 - formData.discount)) / 100).toLocaleString('en-IN')}
                  </p>
                )}
              </div>

              {/* Stock */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  STOCK
                </label>
                <input
                  type="number"
                  name="stock"
                  value={formData.stock}
                  onChange={handleChange}
                  required
                  min="0"
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black"
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <label className="text-xs tracking-widest opacity-50">
                  DESCRIPTION
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                  rows="4"
                  className="w-full bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black resize-none"
                />
              </div>


            </motion.div>
          ) : (
            <motion.div
              key="images"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-8"
            >
              {/* Image URL Input */}
              <div className="space-y-4">
                <div className="flex space-x-4">
                  <input
                    type="url"
                    value={imageUrl}
                    onChange={(e) => setImageUrl(e.target.value)}
                    placeholder="ENTER IMAGE URL"
                    className="flex-1 bg-transparent border-b border-black/10 px-0 py-2 focus:ring-0 focus:border-black text-sm"
                  />
                  <button
                    type="button"
                    onClick={handleAddImage}
                    className="text-sm tracking-widest hover:opacity-70"
                  >
                    ADD
                  </button>
                </div>
              </div>

              {/* Image Grid */}
              <div className="grid grid-cols-2 gap-4">
                {formData.images.map((image, index) => (
                  <div key={index} className="relative aspect-[3/4] bg-gray-50">
                    <Image
                      src={image}
                      alt={`Product ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="absolute top-2 right-2 bg-black text-white text-xs tracking-widest px-3 py-1 hover:opacity-70"
                    >
                      REMOVE
                    </button>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Fixed Bottom Bar */}
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-black/10 p-4">
          <div className="max-w-screen-xl mx-auto flex justify-between items-center">
            <button
              type="button"
              onClick={() => router.back()}
              className="text-sm tracking-widest hover:opacity-70"
            >
              CANCEL
            </button>
            <button
              type="submit"
              disabled={loading}
              className="text-sm tracking-widest bg-black text-white px-8 py-3 hover:opacity-90 disabled:opacity-50"
            >
              {loading ? 'SAVING...' : 'SAVE PRODUCT'}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
