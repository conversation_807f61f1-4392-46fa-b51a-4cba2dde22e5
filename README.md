# FurnitureBazaar 🛋️

<div align="center">
  <img src="./public/docs/architecture-diagram.png" alt="FurnitureBazaar Architecture" width="800px" />
  <p><i>Your Premium Furniture E-commerce Platform</i></p>

  [![Next.js](https://img.shields.io/badge/Next.js-15.2.0-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
  [![React](https://img.shields.io/badge/React-19.0.0-blue?style=for-the-badge&logo=react)](https://reactjs.org/)
  [![MongoDB](https://img.shields.io/badge/MongoDB-8.10.1-green?style=for-the-badge&logo=mongodb)](https://www.mongodb.com/)
  [![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.4.1-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
  [![Razorpay](https://img.shields.io/badge/Razorpay-Payment_Gateway-blue?style=for-the-badge&logo=razorpay)](https://razorpay.com/)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg?style=for-the-badge)](LICENSE)
</div>

## 📋 Table of Contents

- [Overview](#-overview)
- [Features](#-features)
- [Tech Stack](#-tech-stack)
- [Architecture](#-architecture)
- [Installation](#-installation)
- [Environment Variables](#-environment-variables)
- [Usage](#-usage)
- [API Documentation](#-api-documentation)
- [Project Structure](#-project-structure)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [License](#-license)
- [Contact](#-contact)

## 🔍 Overview

FurnitureBazaar is a modern, full-featured e-commerce platform specializing in furniture and home decor. Built with Next.js 15 and MongoDB, it offers a seamless shopping experience with advanced features for both customers and administrators.

The platform combines elegant design with powerful functionality, providing users with an intuitive interface to browse, search, and purchase furniture items while giving administrators robust tools to manage products, orders, and customer relationships.

Designed with a focus on user experience and performance, FurnitureBazaar implements industry best practices for e-commerce, including secure authentication, responsive design, and optimized checkout flows with multiple payment options including Razorpay integration for Indian customers.

## ✨ Features

### 🔐 Authentication & User Management
- Secure JWT-based authentication
- User registration with email verification
- Email existence checking for improved user experience
- Password reset functionality with secure tokens
- Role-based access control (user/admin)
- Protected routes and API endpoints
- User profile management
- Session management with secure cookies

### 🛍️ Shopping Experience
- Responsive product catalog with filtering and sorting
- Advanced product search functionality
- Detailed product pages with specifications
- Real-time shopping cart updates
- Wishlist management
- Secure checkout process with multiple payment options
  - Cash on Delivery (COD)
  - Online payments via Razorpay (Cards, UPI, NetBanking, Wallets)
- Order history and tracking
- Email notifications for orders

### 👨‍💼 Admin Dashboard
- Comprehensive admin interface
- Product management (CRUD operations)
- Inventory tracking
- Order processing and management
- Customer management
- Analytics and reporting

### 📱 Responsive Design
- Mobile-first approach
- Optimized for all device sizes
- Consistent experience across platforms

## 🚀 Tech Stack

### Frontend
- **Framework**: [Next.js 15.2.0](https://nextjs.org/), [React 19.0.0](https://reactjs.org/)
- **Styling**: [TailwindCSS 3.4.1](https://tailwindcss.com/), [Framer Motion 12.4.4](https://www.framer.com/motion/)
- **State Management**: React Context API
- **UI Components**: [React Icons 5.4.0](https://react-icons.github.io/react-icons/)
- **Notifications**: [React Hot Toast 2.5.2](https://react-hot-toast.com/)

### Backend
- **API Routes**: Next.js API Routes
- **Database**: [MongoDB](https://www.mongodb.com/) with [Mongoose 8.10.1](https://mongoosejs.com/)
- **Authentication**: [JWT (jsonwebtoken 9.0.2)](https://github.com/auth0/node-jsonwebtoken)
- **Security**: [bcryptjs 3.0.2](https://github.com/dcodeIO/bcrypt.js/)
- **Date Handling**: [date-fns 4.1.0](https://date-fns.org/)
- **Email Service**: [Resend](https://resend.com/) for transactional emails
- **Payment Processing**: [Razorpay](https://razorpay.com/) for secure online payments

### Development Tools
- **Linting**: [ESLint 9](https://eslint.org/)
- **Build Tool**: [Turbopack](https://turbo.build/pack)

## 🏗️ Architecture

FurnitureBazaar follows a modern architecture pattern with the following key components:

1. **Client Layer** - Next.js frontend with React components
2. **API Layer** - Next.js API routes handling requests
3. **Database Layer** - MongoDB storing application data
4. **Authentication System** - JWT-based auth flow
5. **External Services** - Payment processing, image storage, etc.

For a detailed view of the architecture, see the [Architecture Diagram](./public/docs/architecture-diagram.png).

## 🛠️ Installation

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn
- MongoDB instance (local or Atlas)

### Setup Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/furniturebazaar.git
   cd furniturebazaar
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory with the following variables:
   ```
   MONGODB_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Access the application**
   Open [http://localhost:3000](http://localhost:3000) in your browser

## 🔧 Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `MONGODB_URI` | MongoDB connection string | Yes |
| `JWT_SECRET` | Secret key for JWT token generation | Yes |
| `NEXT_PUBLIC_APP_URL` | Base URL for the application (used in emails) | Yes |
| `NEXT_PUBLIC_API_URL` | Base URL for API (in production) | No |
| `NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME` | Cloudinary cloud name for image storage | No |
| `RAZORPAY_KEY_ID` | Razorpay API Key ID | For online payments |
| `RAZORPAY_KEY_SECRET` | Razorpay API Key Secret | For online payments |
| `RESEND_API_KEY` | Resend API key for sending emails | For email functionality |

## 📖 Usage

### Customer Features

1. **Browse Products**
   - Navigate through categories
   - Use filters to refine search
   - View detailed product information
   - Square aspect ratio product images for consistent display

2. **Account Management**
   - Register a new account with email verification
   - Check if email already exists in the system
   - Log in to existing account
   - Reset forgotten password
   - Update profile information
   - Change password
   - View order history

3. **Shopping**
   - Add products to cart
   - Manage cart items
   - Save items to wishlist
   - Complete checkout process with multiple payment options
   - Receive order confirmation emails

### Admin Features

1. **Access Admin Dashboard**
   - Log in with admin credentials
   - Navigate to `/admin` route

2. **Manage Products**
   - Add new products
   - Edit existing products
   - Update inventory
   - Manage product categories

3. **Process Orders**
   - View all orders
   - Update order status
   - Process refunds
   - Generate reports

## 📚 API Documentation

FurnitureBazaar provides a comprehensive API for interacting with the platform. For detailed API documentation, see [API-DOCUMENTATION.md](./API-DOCUMENTATION.md).

### Key Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/check-email` - Check if email exists
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token
- `GET /api/auth/verify-email` - Verify email address
- `GET /api/auth/check` - Check authentication status

#### Products
- `GET /api/products` - Fetch all products
- `GET /api/products/[id]` - Fetch single product
- `POST /api/products` - Create product (admin only)
- `PUT /api/products/[id]` - Update product (admin only)
- `DELETE /api/products/[id]` - Delete product (admin only)

#### Orders
- `POST /api/orders` - Create new order
- `GET /api/orders` - Fetch user orders
- `GET /api/orders/[id]` - Fetch single order
- `PUT /api/orders/[id]` - Update order status (admin only)

#### Payment
- `POST /api/payment/razorpay` - Create Razorpay payment order
- `PUT /api/payment/razorpay` - Verify Razorpay payment
- `POST /api/payment/razorpay/webhook` - Handle Razorpay webhooks

#### Newsletter
- `POST /api/newsletter` - Subscribe to newsletter

## 📁 Project Structure

```
furniturebazaar/
├── public/                # Static assets
│   ├── docs/             # Documentation assets
│   └── images/           # Image assets
├── src/
│   ├── app/              # Next.js app directory
│   │   ├── api/          # API routes
│   │   ├── admin/        # Admin pages
│   │   ├── products/     # Product pages
│   │   ├── cart/         # Cart page
│   │   ├── checkout/     # Checkout pages
│   │   ├── account/      # User account pages
│   │   ├── layout.js     # Root layout
│   │   └── page.js       # Home page
│   ├── components/       # Reusable UI components
│   │   ├── ui/           # Basic UI components
│   │   ├── layout/       # Layout components
│   │   ├── product/      # Product-related components
│   │   ├── cart/         # Cart components
│   │   └── admin/        # Admin components
│   ├── context/          # React Context providers
│   │   ├── AuthContext.js
│   │   ├── CartContext.js
│   │   └── UIContext.js
│   ├── lib/              # Utility functions
│   │   ├── auth.js       # Authentication utilities
│   │   ├── jwt.js        # JWT handling
│   │   └── mongodb.js    # Database connection
│   ├── models/           # MongoDB schemas
│   │   ├── Contact.js
│   │   ├── Order.js
│   │   ├── Product.js
│   │   └── User.js
│   └── middleware.js     # Next.js middleware
├── .env.local            # Environment variables
├── next.config.mjs       # Next.js configuration
├── tailwind.config.mjs   # TailwindCSS configuration
├── package.json          # Project dependencies
└── README.md             # Project documentation
```

## 🚀 Deployment

FurnitureBazaar is configured for easy deployment on Vercel.

### Deploy to Vercel

1. Push your code to a GitHub repository
2. Connect your repository to Vercel
3. Configure environment variables in the Vercel dashboard
   - Make sure to set all required environment variables, including Razorpay API keys and Resend API key
4. Deploy

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fyourusername%2Ffurniturebazaar)

### Important Deployment Notes

- **Environment Variables**: Ensure all environment variables are properly set in your deployment environment
- **Razorpay Integration**: For the payment system to work correctly, valid Razorpay API keys must be configured
- **Email Functionality**: Set up Resend API key for email verification, password reset, and order notifications

### Alternative Deployment Options

- **Docker**: Containerize the application for deployment on any platform supporting Docker
- **Traditional Hosting**: Build the application and deploy to any Node.js hosting service

## 🤝 Contributing

We welcome contributions to FurnitureBazaar! Please follow these steps:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Commit your changes (`git commit -m 'Add some amazing feature'`)
5. Push to the branch (`git push origin feature/amazing-feature`)
6. Open a Pull Request

For major changes, please open an issue first to discuss what you would like to change.

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📧 Contact

Project Maintainer: Your Name - [@yourtwitter](https://twitter.com/yourtwitter) - <EMAIL>

Project Link: [https://github.com/yourusername/furniturebazaar](https://github.com/yourusername/furniturebazaar)

## 🔄 Recent Updates

- Added email existence checking for improved user experience
- Integrated Razorpay payment gateway for Indian customers
- Enhanced password reset flow with email verification
- Improved responsive design with consistent image aspect ratios
- Added comprehensive email notification system

---

<div align="center">
  <p>⭐ Star us on GitHub — it helps!</p>
  <p>Made with ❤️ by the FurnitureBazaar Team</p>
</div>
