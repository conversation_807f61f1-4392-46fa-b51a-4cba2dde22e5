{"name": "funiturebazaar", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "framer-motion": "^12.4.4", "jsonwebtoken": "^9.0.2", "mongoose": "^8.12.1", "next": "^15.2.0", "razorpay": "^2.9.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "recharts": "^2.15.1", "resend": "^4.1.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}